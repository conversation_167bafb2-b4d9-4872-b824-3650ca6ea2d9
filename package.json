{"name": "backend", "version": "1.0.0", "type": "module", "description": "", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "start:prod": "NODE_ENV=prod node app.js", "start:cron": "node cron.js", "logs:clean": "rm -rf logs/*"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"aws-sdk": "^2.1692.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.3.1", "expo-server-sdk": "^3.15.0", "express": "^4.21.2", "express-session": "^1.17.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "mysql2": "^3.14.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.9", "nodemailer-express-handlebars": "^6.1.2", "razorpay": "^2.9.5", "sequelize": "^6.37.7", "uuid": "^9.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/preset-env": "^7.25.8", "babel-jest": "^29.7.0", "babel-register": "^6.26.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "sequelize-cli": "^6.6.3", "supertest": "^7.0.0"}}