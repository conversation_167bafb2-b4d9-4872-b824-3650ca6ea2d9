<style>
  /* Reset & Body Styles */
  body {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    /* Helvetica Neue might not render everywhere */
    color: #333333;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    -webkit-font-smoothing: antialiased;
    /* Better font rendering on WebKit */
    -moz-osx-font-smoothing: grayscale;
    /* Better font rendering on Firefox */
    font-size: 16px;
    /* Base font size */
    line-height: 1.5;
  }

  /* Container */
  .container {
    width: 100%;
    max-width: 600px;
    margin: 30px auto;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    /* Ensures child margins don't collapse */
  }

  /* Header */
  .header {
    text-align: center;
    padding: 20px 0;
    border-bottom: 1px solid #eeeeee;
    /* Subtle separator */
  }

  .header img {
    width: 120px;
    max-width: 100%;
    /* Prevent overflow */
    height: auto;
    /* Maintain aspect ratio */
  }

  /* Content Area */
  .content {
    padding: 20px 30px;
    /* Increased horizontal padding */
    text-align: center;
  }

  .content h1 {
    color: #333333;
    font-size: 24px;
    margin-top: 0;
    /* Remove default top margin */
    margin-bottom: 20px;
  }

  .content p {
    margin: 10px 0;
    font-size: 16px;
    line-height: 1.6;
    /* Slightly increased line-height */
    text-align: left;
    /* Default paragraphs to left align */
  }

  .content p.intro {
    /* Specific class for intro paragraphs if needed */
    text-align: center;
  }


  /* Details Section (for tables) */
  .details {
    background-color: #f9f9f9;
    /* Slightly different background */
    border-radius: 8px;
    padding: 20px;
    margin: 25px 0;
    text-align: left;
    border: 1px solid #eeeeee;
    /* Subtle border */
  }

  .details p {
    margin: 5px 0;
    font-size: 14px;
  }

  .details p.section-title {
    /* Style for the title above the table */
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: bold;
    color: #555555;
  }

  .details table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    /* Space between title and table */
  }

  .details th,
  .details td {
    border: 1px solid #dddddd;
    padding: 10px;
    /* Increased padding */
    text-align: left;
    font-size: 14px;
    line-height: 1.4;
  }

  .details th {
    background-color: #f0f0f0;
    /* Header background */
    font-weight: bold;
    color: #333333;
  }

  .details td {
    color: #555555;
  }

  .details .note {
    margin-top: 15px;
    font-size: 14px;
    color: #777777;
    background-color: #fffacd;
    /* Light yellow background */
    padding: 10px;
    border-left: 3px solid #ffd700;
    /* Yellow left border */
    border-radius: 4px;
  }

  .details .note p {
    margin: 0;
  }

  /* Steps / Instructions Section (Travel, etc.) */
  .steps {
    margin: 25px 0;
    padding: 20px;
    border-top: 1px solid #eeeeee;
    /* Separator */
    text-align: left;
  }

  .steps h2 {
    font-size: 18px;
    color: #444444;
    margin-top: 0;
    margin-bottom: 15px;
  }

  .steps ul {
    margin: 15px 0;
    padding-left: 20px;
    /* Indent list */
  }

  .steps li {
    margin-bottom: 8px;
    color: #555555;
  }

  .steps p {
    margin: 15px 0;
  }

  .steps .link {
    /* Style for links within steps */
    color: #007bff;
    text-decoration: underline;
  }

  .steps .highlight {
    /* Style for highlighted text */
    background-color: #fff3cd;
    color: #856404;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
  }

  /* Footer */
  .footer {
    text-align: center;
    padding: 20px 30px;
    font-size: 13px;
    /* Smaller font size */
    color: #999999;
    border-top: 1px solid #eeeeee;
    margin-top: 20px;
  }

  .link {
    color: #1a73e8;
    text-decoration: none;
  }
  
  .link:hover {
    text-decoration: underline;
  }

  /* Responsive Styles */
  @media screen and (max-width: 600px) {
    .container {
      width: 100% !important;
      margin: 0 auto !important;
      border-radius: 0 !important;
      box-shadow: none !important;
    }

    .content {
      padding: 15px !important;
      /* Reduce padding */
    }

    .content h1 {
      font-size: 20px !important;
      /* Reduce heading size */
    }

    .content p,
    .details p,
    .steps p,
    .steps li {
      font-size: 15px !important;
      /* Slightly smaller text */
    }

    .details {
      padding: 15px !important;
    }

    .details th,
    .details td {
      padding: 8px !important;
      /* Reduce table padding */
      font-size: 13px !important;
    }

    .steps {
      padding: 15px !important;
    }

    .steps h2 {
      font-size: 16px !important;
    }

    .footer {
      padding: 15px !important;
    }
  }
</style>