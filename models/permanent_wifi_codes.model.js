import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';
import { STATUS_PENDING, STATUS_APPROVED, STATUS_REJECTED } from '../config/constants.js';

const PermanentWifiCodes = sequelize.define(
  'PermanentWifiCodes',
  {
    id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    cardno: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      references: {
        model: 'card_db',
        key: 'cardno'
      }
    },
    permanent_code: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'The actual permanent WiFi code assigned by admin'
    },
    request_status: {
      type: DataTypes.ENUM,
      allowNull: false,
      values: [STATUS_PENDING, STATUS_APPROVED, STATUS_REJECTED],
      defaultValue: STATUS_PENDING,
      comment: 'Status of the permanent code request'
    },
    requested_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'When the user requested the permanent code'
    },
    approved_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When the admin approved/rejected the request'
    },
    approved_by: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Admin username who approved/rejected the request'
    },
    admin_comments: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Admin comments for approval/rejection'
    }
  },
  {
    tableName: 'permanent_wifi_codes',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
);

export default PermanentWifiCodes;
