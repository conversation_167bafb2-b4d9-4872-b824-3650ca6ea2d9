import XLSX from 'xlsx';
// import WifiPwd from '../../models/wifi.model.js'; // adjust path if needed
import {
  WifiDb,
  CardDb,
  FlatBooking,
  RoomBooking,
  PermanentWifiCodes
} from '../../models/associations.js';
// import logger from '../../config/logger.js';
import database from '../../config/database.js';
import Sequelize from 'sequelize';
import {
  STATUS_PENDING,
  STATUS_APPROVED,
  STATUS_REJECTED
} from '../../config/constants.js';
import APIError from '../../utils/ApiError.js';
// import moment from 'moment';

export const uploadWiFiCodes = async (req, res) => {
  try {
    const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
    const sheet = XLSX.utils.sheet_to_json(
      workbook.Sheets[workbook.SheetNames[0]],
      { defval: '' }
    );

    const formattedRows = [];

    for (const row of sheet) {
      const createdAt = new Date(); // automatically use current timestamp

      formattedRows.push({
        cardno: null,
        password: row.password,
        roombookingid: null,
        status: 'active',
        updatedBy: req.user?.username || 'wifiAdmin', // fallback
        created_at: createdAt // ✅ your DB expects this name
      });
    }

    if (formattedRows.length === 0) {
      return res
        .status(400)
        .json({ error: 'No valid rows found with correct date format.' });
    }

    const incomingPwd = formattedRows.map((row) => row.password);

    const existingRecords = await WifiDb.findAll({
      where: { password: incomingPwd },
      attributes: ['password'],
      raw: true
    });

    const existingPwd = new Set(existingRecords.map((r) => r.password));

    const uniqueRows = formattedRows.filter(
      (row) => !existingPwd.has(row.password)
    );

    if (uniqueRows.length === 0) {
      return res.status(200).json({
        message: 'No new rows to insert. All passwords were duplicates.'
      });
    }

    await WifiDb.bulkCreate(uniqueRows);

    res.status(200).json({
      message: `${uniqueRows.length} new record(s) inserted. ${
        formattedRows.length - uniqueRows.length
      } duplicate(s) ignored.`
    });
  } catch (err) {
    console.error('Error processing Excel upload:', err);
    res.status(500).json({
      error: 'Failed to process and store Excel data: ' + err.message
    });
  }
};

export const wifiRecord = async (req, res) => {
  const { startDate, endDate, status, bookingType } = req.query;

  let whereClause = 'WHERE 1 = 1';
  const replacements = {};

  if (startDate && endDate) {
    whereClause += ' AND DATE(wp.updatedAt) BETWEEN :startDate AND :endDate';
    replacements.startDate = startDate;
    replacements.endDate = endDate;
  }

  if (status && status !== 'all') {
    whereClause += ' AND wp.status = :status';
    replacements.status = status;
  }

  if (bookingType === 'room') {
    whereClause += ' AND rb.bookingid IS NOT NULL';
  } else if (bookingType === 'flat') {
    whereClause += ' AND fb.bookingid IS NOT NULL';
  }

  const query = `
    SELECT 
      wp.cardno,
      wp.password,
      wp.roombookingid,
      wp.status,
      wp.updatedAt AS wifi_updatedAt,

      cd.issuedto,
      cd.mobno,
      cd.email,

      rb.checkin AS room_checkin,
      rb.checkout AS room_checkout,
      rb.updatedAt AS room_updatedAt,

      fb.checkin AS flat_checkin,
      fb.checkout AS flat_checkout,
      fb.updatedAt AS flat_updatedAt

    FROM wifi_pwd AS wp

    LEFT JOIN card_db AS cd ON wp.cardno = cd.cardno
    LEFT JOIN room_booking AS rb ON wp.roombookingid = rb.bookingid
    LEFT JOIN flat_booking AS fb ON wp.roombookingid = fb.bookingid

    ${whereClause}
    ORDER BY wp.updatedAt DESC;
  `;

  try {
    const result = await database.query(query, {
      type: Sequelize.QueryTypes.SELECT,
      replacements
    });

    res.status(200).json({ message: 'Success', data: result });
  } catch (err) {
    console.error('Error fetching wifi records:', err);
    res.status(500).json({ error: 'Failed to fetch wifi records' });
  }
};

// Permanent WiFi Code Management Functions
export const getPermanentCodeRequests = async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {};
    if (
      status &&
      [STATUS_PENDING, STATUS_APPROVED, STATUS_REJECTED].includes(status)
    ) {
      whereClause.request_status = status;
    }

    const { count, rows } = await PermanentWifiCodes.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: CardDb,
          attributes: ['cardno', 'issuedto', 'email', 'mobno', 'res_status']
        }
      ],
      order: [['requested_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.status(200).json({
      message: 'Permanent WiFi code requests fetched successfully',
      data: {
        requests: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching permanent code requests:', error);
    res.status(500).json({ error: 'Failed to fetch permanent code requests' });
  }
};

export const approvePermanentCodeRequest = async (req, res) => {
  const t = await database.transaction();

  try {
    const { requestId } = req.params;
    const { permanent_code, admin_comments } = req.body;

    if (!permanent_code) {
      await t.rollback();
      return res
        .status(400)
        .json({ error: 'Permanent code is required for approval' });
    }

    // Check if the request exists and is pending
    const request = await PermanentWifiCodes.findByPk(requestId, {
      transaction: t
    });

    if (!request) {
      await t.rollback();
      return res
        .status(404)
        .json({ error: 'Permanent code request not found' });
    }

    if (request.request_status !== STATUS_PENDING) {
      await t.rollback();
      return res.status(400).json({
        error: `Request has already been ${request.request_status}`
      });
    }

    // Check if the permanent code is already assigned to another user
    const existingCode = await PermanentWifiCodes.findOne({
      where: {
        permanent_code,
        request_status: STATUS_APPROVED
      },
      transaction: t
    });

    if (existingCode) {
      await t.rollback();
      return res.status(400).json({
        error: 'This permanent code is already assigned to another user'
      });
    }

    // Update the request
    await request.update(
      {
        request_status: STATUS_APPROVED,
        permanent_code,
        approved_at: new Date(),
        approved_by: req.user?.username || 'admin',
        admin_comments
      },
      { transaction: t }
    );

    await t.commit();

    res.status(200).json({
      message: 'Permanent WiFi code request approved successfully',
      data: request
    });
  } catch (error) {
    await t.rollback();
    console.error('Error approving permanent code request:', error);
    res.status(500).json({ error: 'Failed to approve permanent code request' });
  }
};

export const rejectPermanentCodeRequest = async (req, res) => {
  const t = await database.transaction();

  try {
    const { requestId } = req.params;
    const { admin_comments } = req.body;

    // Check if the request exists and is pending
    const request = await PermanentWifiCodes.findByPk(requestId, {
      transaction: t
    });

    if (!request) {
      await t.rollback();
      return res
        .status(404)
        .json({ error: 'Permanent code request not found' });
    }

    if (request.request_status !== STATUS_PENDING) {
      await t.rollback();
      return res.status(400).json({
        error: `Request has already been ${request.request_status}`
      });
    }

    // Update the request
    await request.update(
      {
        request_status: STATUS_REJECTED,
        approved_at: new Date(),
        approved_by: req.user?.username || 'admin',
        admin_comments
      },
      { transaction: t }
    );

    await t.commit();

    res.status(200).json({
      message: 'Permanent WiFi code request rejected successfully',
      data: request
    });
  } catch (error) {
    await t.rollback();
    console.error('Error rejecting permanent code request:', error);
    res.status(500).json({ error: 'Failed to reject permanent code request' });
  }
};
