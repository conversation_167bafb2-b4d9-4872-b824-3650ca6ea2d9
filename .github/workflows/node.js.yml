# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Node.js CI/CD

on:
  push:
    branches: [ "main" ]

jobs:
  build:
    runs-on: self-hosted

    strategy:
      matrix:
        node-version: [23.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:

    - name: Fix permissions on project directory
      run: |
        pwd
        ls -lrth
        sudo chown -R ubuntu:ubuntu /home/<USER>/actions-runner-api/_work/aashray-backend/aashray-backend
    
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci

    - name: Set environment file
      run: |
        touch .env.prod
        echo "${{ secrets.PROD_ENV_FILE }}" > .env.prod

    - name: runs all pending Sequelize migrations
      run: |
        NODE_ENV=prod npx sequelize db:migrate --debug

    - name: Reload PM2 process to avoid downtime   
      run: |
        sudo pm2 reload BackendAPI --update-env
        sudo pm2 reload CronJob --update-env

    - name: Status PM2 process    
      run: |
        sudo pm2 list

    - name: Show PM2 process    
      run: |
        sudo pm2 show BackendAPI
        sudo pm2 show CronJob
    
