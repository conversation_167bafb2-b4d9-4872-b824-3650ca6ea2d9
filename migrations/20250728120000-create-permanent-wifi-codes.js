'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('permanent_wifi_codes', {
      id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true
      },
      cardno: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
        references: {
          model: 'card_db',
          key: 'cardno'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      permanent_code: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'The actual permanent WiFi code assigned by admin'
      },
      request_status: {
        type: Sequelize.ENUM,
        allowNull: false,
        values: ['pending', 'approved', 'rejected'],
        defaultValue: 'pending',
        comment: 'Status of the permanent code request'
      },
      requested_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        comment: 'When the user requested the permanent code'
      },
      approved_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'When the admin approved/rejected the request'
      },
      approved_by: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Admin username who approved/rejected the request'
      },
      admin_comments: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Admin comments for approval/rejection'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes for better performance
    await queryInterface.addIndex('permanent_wifi_codes', ['cardno'], {
      name: 'permanent_wifi_codes_cardno_idx',
      unique: true
    });

    await queryInterface.addIndex('permanent_wifi_codes', ['request_status'], {
      name: 'permanent_wifi_codes_status_idx'
    });

    // Add unique index for permanent_code (only for non-null values)
    await queryInterface.sequelize.query(`
      CREATE UNIQUE INDEX permanent_wifi_codes_code_idx
      ON permanent_wifi_codes (permanent_code)
      WHERE permanent_code IS NOT NULL
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      'DROP INDEX IF EXISTS permanent_wifi_codes_code_idx'
    );
    await queryInterface.removeIndex(
      'permanent_wifi_codes',
      'permanent_wifi_codes_status_idx'
    );
    await queryInterface.removeIndex(
      'permanent_wifi_codes',
      'permanent_wifi_codes_cardno_idx'
    );
    await queryInterface.dropTable('permanent_wifi_codes');
  }
};
