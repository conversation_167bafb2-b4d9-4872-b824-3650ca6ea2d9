import express from 'express';
const router = express.Router();

import { auth, authorizeRoles } from '../../middleware/AdminAuth.js';
import { ROLE_WIFI_ADMIN, ROLE_SUPER_ADMIN } from '../../config/constants.js';
import {
  uploadWiFiCodes,
  wifiRecord,
  getPermanentCodeRequests,
  approvePermanentCodeRequest,
  rejectPermanentCodeRequest
} from '../../controllers/admin/wifiManagement.controller.js';
import CatchAsync from '../../utils/CatchAsync.js';
import multer from 'multer';

const upload = multer({ storage: multer.memoryStorage() });

router.use(auth);
router.use(authorizeRoles(ROLE_SUPER_ADMIN, ROLE_WIFI_ADMIN));

// Temporary WiFi codes
router.post('/uploadcode', upload.single('file'), CatchAsync(uploadWiFiCodes));
router.get('/wifirecords', CatchAsync(wifiRecord));

// Permanent WiFi code management
router.get('/permanent/requests', CatchAsync(getPermanentCodeRequests));
router.put(
  '/permanent/approve/:requestId',
  CatchAsync(approvePermanentCodeRequest)
);
router.put(
  '/permanent/reject/:requestId',
  CatchAsync(rejectPermanentCodeRequest)
);

export default router;
