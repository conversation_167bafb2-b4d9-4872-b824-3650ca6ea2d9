import { Sequelize } from 'sequelize';
const { private_key } =
  process.env.NODE_ENV == 'qa' && JSON.parse(process.env.DB_CERT);

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USERNAME,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'mysql',
    dialectOptions: {
      decimalNumbers: true,
      ssl: process.env.NODE_ENV == 'qa' && {
        ca: private_key
      }
    },
    pool: { max: 5, idle: 30000 },
    language: 'en'
  }
);

export default sequelize;
