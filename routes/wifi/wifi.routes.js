import express from 'express';
const router = express.Router();
import {
  generatePassword,
  getPassword,
  requestPermanentCode,
  getPermanentCodeStatus
} from '../../controllers/wifi/wifi.controller.js';
import { validateCard } from '../../middleware/validate.js';
import catchAsync from '../../utils/CatchAsync.js';

// Temporary WiFi codes
router.get('/', validateCard, catchAsync(getPassword));
router.get('/generate', validateCard, catchAsync(generatePassword));

// Permanent WiFi codes
router.post(
  '/permanent/request',
  validateCard,
  catchAsync(requestPermanentCode)
);
router.get(
  '/permanent/status',
  validateCard,
  catchAsync(getPermanentCodeStatus)
);

export default router;
