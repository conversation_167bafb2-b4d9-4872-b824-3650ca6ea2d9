<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Confirmation</title>

    {{> styles }}
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="https://i.imgur.com/SkOGGbd.png" alt="Company Logo">
        </div>
        <div class="content" >
            <p class="intro">Dear {{name}},</p> <p class="intro"> {{welcomeMessage}} Here are the details of your bookings:</p>
           
         {{#if showUtsavDetail}}
            <div class="details">
                <p class="section-title"><strong>Raj Utsav Booking:</strong></p>
                <table>
                    <thead> <tr>
                            <th>BookingId</th>
                            <th>Utsav Name</th>
                            <th>Name</th>     
                            <th>Start Date</th>
                            <th>End Date</th> 
                            <th>Package</th> 
                            <th>Status</th>
                            </tr>
                    </thead>
                    <tbody> {{#each utsavBookingDetails}}
                        <tr>
                            <td>{{bookingid}}</td>
                            <td>{{utsavname}}</td>
                            <td>{{name}}</td>
                            <td>{{startdate}}</td>
                            <td>{{enddate}}</td>
                            <td>{{package}}</td>
                            <td><b><span style="color: blue;">{{status}}</span></b></td>
                        </tr>
                        {{/each}}
                    </tbody>
                </table>
            </div>
            {{/if}}


             {{#if showRoomDetail}}
            <div class="details">
                <p class="section-title"><strong>Raj Sharan - Room Booking:</strong></p>
                <table>
                    <thead> <tr>
                            <th>BookingId</th>
                            <th>Name</th>
                            <th>Checkin</th>
                            <th>Checkout</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody> {{#each roomBookingDetails}}
                        <tr>
                            <td>{{bookingid}}</td>
                            <td>{{name}}</td>
                            <td>{{checkin}}</td>
                            <td>{{checkout}}</td>
                            <td><span style="color: blue;">{{status}}</span></td>
                        </tr>
                        {{/each}}
                    </tbody>
                </table>
            </div>
            {{/if}}

            {{#if showFlatDetail}}
            <div class="details">
                 <p class="section-title"><strong>Raj Sharan - Flat Booking:</strong></p>
                 <table>
                     <thead>
                        <tr>
                            <th>BookingId</th>
                            <th>Name</th>
                            <th>Flat No.</th>
                            <th>Checkin</th>
                            <th>Checkout</th>
                            <th>Status</th>
                        </tr>
                     </thead>
                     <tbody>
                        {{#each flatBookingDetails}}
                        <tr>
                            <td>{{bookingid}}</td>
                            <td>{{name}}</td>
                            <td>{{flatno}}</td>
                            <td>{{checkin}}</td>
                            <td>{{checkout}}</td>
                            <td><span style="color: blue;">{{status}}</span></td>
                        </tr>
                        {{/each}}
                     </tbody>
                </table>
            </div>
            {{/if}}

            {{#if showAdhyanDetail}}
             <div class="details" >
                <p class="section-title"><strong>Raj Adhyayan Booking:</strong></p>
                <table>
                   <thead>
                        <tr>
                           <th>BookingId</th>
                           <th>Name</th>
                           <th>Adhyayan Name</th>
                           <th>Speaker</th>
                           <th>Start Date</th>
                           <th>End Date</th>
                           <th>Status</th>
                        </tr>
                   </thead>
                   <tbody>
                     {{#each adhyanBookingDetails}}
                        <tr>
                            <td>{{bookingid}}</td>
                            <td>{{name}}</td>
                            <td>{{adhyayanname}}</td>
                            <td>{{speaker}}</td>
                            <td>{{startdate}}</td>
                            <td>{{enddate}}</td>
                            <td><span style="color: blue;">{{status}}</span></td>
                        </tr>
                    {{/each}}
                   </tbody>
                </table>

                <div class="note">
                    <p><strong>Please note</strong> that the adhyayan committee reserves the right to cancel your booking within 7 days if you do not meet the adhyayan criteria.</p>
                </div>
            </div>
           {{/if}}

           {{#if showTravelDetail}}
             <div class="details" >
                <p class="section-title"><strong>Raj Pravas - Travel Booking:</strong></p>
                <table>
                   <thead>
                       <tr>
                           <th>BookingId</th>
                           <th>Name</th>
                           <th>Date</th>
                           <th>Pickup Point</th>
                           <th>Dropoff Point</th>
                           <th>Status</th>
                       </tr>
                   </thead>
                   <tbody>
                     {{#each travelBookingDetails}}
                        <tr>
                            <td>{{bookingid}}</td>
                            <td>{{name}}</td>
                            <td>{{date}}</td>
                            <td>{{pickuppoint}}</td>
                            <td>{{dropoffpoint}}</td>
                            <td><b><span style="color: blue;">{{status}}</span></b></td>
                        </tr>
                    {{/each}}
                   </tbody>
                </table>
            </div>
            
            {{> _rajPravasConfirmation}}
            {{/if}}

            <div class="details">
                <p class="section-title"><strong>Booking Status Definitions</strong></p>
                <p><b>pending:</b> This means you need to complete your payment within 24 hours to secure your booking on the Vitraag Vigyaan Aashray mobile app.</p>
                <p><b>waiting:</b> This indicates that your booking has been received and is awaiting confirmation from the administrator.</p>
                <p><b>awaiting confirmation:</b> Your travel booking details have been submitted and are being reviewed by the administrator. 
                          Once reviewed, the status will change to <b>proceed for payment.</b></p>
                <p><b>proceed for payment:</b> Your travel booking is approved and payment need to be made on the Vitraag Vigyaan Aashray mobile app.</p>
                <p><b>confirmed:</b> Great news! Your booking has been successfully confirmed.</p>
                <p><b>cancelled:</b> Your booking has been cancelled by you.</p>
                <p><b>cancelled as wrong form filled:</b> Your travel booking has been cancelled by the administrator because of wrong form filled.</p>
                <p><b>admin cancelled:</b> Your travel booking has been cancelled by the administrator because of non payment.</p>
            </div>


            <div class="steps">
              <p>You can view all your bookings, booking statuses and make any pending payments on the <a href="https://aashray.vitraagvigyaan.org">Vitraag Vigyaan Aashray mobile app</a>.</p>
              <p> <a href='https://drive.google.com/file/d/1nTqWPlsiFZUc_rulepkbo7eLYaM3MidM/view' target='_blank' class="link">Please click here</a> to read the guidelines for your stay at the Research Center. We hope you have a spiritually blissful stay.</p>
              <p>If you have any questions or need further assistance, please feel free to contact us at <b>+91-7875432613 / +91-9004273512</b>.</p>
           </div>
       </div>

        {{> _footer}}
    </div>
  </body>
</html>